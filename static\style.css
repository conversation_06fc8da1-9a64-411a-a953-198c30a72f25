/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    direction: rtl;
}

.container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 500px;
    width: 90%;
}

/* العنوان الرئيسي */
header h1 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

/* معلومات اللعبة */
.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 15px;
}

.current-player {
    font-size: 1.3em;
    font-weight: bold;
}

.player-x {
    color: #e53e3e;
    text-shadow: 1px 1px 2px rgba(229, 62, 62, 0.3);
}

.player-o {
    color: #3182ce;
    text-shadow: 1px 1px 2px rgba(49, 130, 206, 0.3);
}

/* زر إعادة التشغيل */
.reset-button {
    background: linear-gradient(45deg, #48bb78, #38a169);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    font-size: 1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
}

.reset-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(72, 187, 120, 0.4);
}

.reset-button:active {
    transform: translateY(0);
}

/* رسائل اللعبة */
.game-message {
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
    font-size: 1.4em;
    font-weight: bold;
    transition: all 0.3s ease;
}

.game-message.winner {
    background: linear-gradient(45deg, #48bb78, #68d391);
    color: white;
    animation: celebration 0.6s ease-in-out;
}

.game-message.draw {
    background: linear-gradient(45deg, #ed8936, #f6ad55);
    color: white;
}

.game-message.hidden {
    display: none;
}

@keyframes celebration {
    0% { transform: scale(0.8); opacity: 0; }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); opacity: 1; }
}

/* لوحة اللعبة */
.game-board {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 8px;
    max-width: 300px;
    margin: 0 auto 30px;
    background: #2d3748;
    padding: 8px;
    border-radius: 15px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* خلايا اللعبة */
.cell {
    aspect-ratio: 1;
    background: white;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    user-select: none;
}

.cell:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    background: #f7fafc;
}

.cell.filled {
    cursor: not-allowed;
    animation: fillCell 0.3s ease-in-out;
}

.cell.filled:hover {
    transform: none;
}

@keyframes fillCell {
    0% { transform: scale(0.8); opacity: 0.5; }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); opacity: 1; }
}

/* التذييل */
footer {
    margin-top: 20px;
    color: #718096;
    font-size: 0.9em;
}

/* تصميم متجاوب */
@media (max-width: 480px) {
    .container {
        padding: 20px;
        margin: 10px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    .game-info {
        flex-direction: column;
        gap: 10px;
    }
    
    .current-player {
        font-size: 1.1em;
    }
    
    .game-board {
        max-width: 250px;
    }
    
    .cell {
        font-size: 2em;
    }
    
    .reset-button {
        padding: 10px 16px;
        font-size: 0.9em;
    }
}

/* تأثيرات إضافية */
.cell.player-x {
    color: #e53e3e;
    text-shadow: 2px 2px 4px rgba(229, 62, 62, 0.3);
}

.cell.player-o {
    color: #3182ce;
    text-shadow: 2px 2px 4px rgba(49, 130, 206, 0.3);
}

/* تأثير النبض للاعب الحالي */
.current-player span:last-child {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}
