# 🎮 لعبة XO - تيك تاك تو (إصدار ويب)

مشروع لعبة XO (تيك تاك تو) تفاعلية باستخدام Python و Flask مع واجهة ويب عربية جميلة.

## 📋 المكونات الرئيسية

- **لغة البرمجة**: Python 3.x
- **إطار العمل**: Flask
- **الواجهة الأمامية**: HTML5, CSS3, JavaScript
- **دعم اللغة العربية**: نعم (واجهة RTL)
- **التصميم**: متجاوب ومتوافق مع الأجهزة المحمولة

## ✨ الوظائف الأساسية

- ✅ عرض شبكة اللعبة (3x3) تفاعلية
- ✅ التبديل بين اللاعبين (X و O) تلقائيًا
- ✅ التحقق من وجود فائز أو تعادل
- ✅ عرض رسائل الفوز والتعادل مع تأثيرات بصرية
- ✅ زر لإعادة تشغيل اللعبة
- ✅ تصميم عربي جميل مع دعم RTL
- ✅ تأثيرات بصرية وانيميشن
- ✅ تصميم متجاوب للأجهزة المختلفة

## 📁 هيكلية المشروع

```
xo_game/
│
├── app.py                # الكود الخلفي بلغة Python (Flask)
├── static/
│   └── style.css         # تنسيق اللعبة
├── templates/
│   └── index.html        # واجهة اللعبة
└── README.md             # وصف اللعبة وطريقة التشغيل
```

## 🚀 طريقة التشغيل

### 1. تثبيت المتطلبات

تأكد من تثبيت Python 3.x على جهازك، ثم قم بتثبيت Flask:

```bash
pip install flask
```

### 2. تشغيل التطبيق

```bash
python app.py
```

### 3. فتح اللعبة

افتح المتصفح وانتقل إلى:
```
http://localhost:5544
```

## 🎯 كيفية اللعب

1. **البداية**: اللاعب X يبدأ أولاً
2. **اللعب**: اضغط على أي مربع فارغ لتسجيل حركتك
3. **التبديل**: يتم التبديل تلقائيًا بين اللاعبين
4. **الفوز**: أول لاعب يحصل على 3 رموز متتالية (أفقي، عمودي، أو قطري) يفوز
5. **التعادل**: إذا امتلأت جميع المربعات دون فائز
6. **لعبة جديدة**: اضغط على زر "🔄 لعبة جديدة" لبدء لعبة جديدة

## 🎨 المميزات التقنية

### الواجهة الأمامية
- تصميم عربي أنيق مع دعم RTL
- تأثيرات بصرية وانيميشن
- تصميم متجاوب للأجهزة المختلفة
- ألوان متدرجة وظلال جميلة
- تفاعل سلس مع المستخدم

### الواجهة الخلفية
- API RESTful باستخدام Flask
- فئة XOGame لإدارة منطق اللعبة
- التحقق من صحة الحركات
- إدارة حالة اللعبة
- معالجة الأخطاء

### نقاط النهاية (API Endpoints)

- `GET /` - الصفحة الرئيسية
- `GET /api/game_state` - الحصول على حالة اللعبة
- `POST /api/make_move` - تسجيل حركة اللاعب
- `POST /api/reset_game` - إعادة تشغيل اللعبة

## 🔧 التخصيص والتطوير

يمكنك تطوير اللعبة بإضافة المميزات التالية:

### مميزات مقترحة للتطوير
- 🤖 اللعب ضد الكمبيوتر (AI) باستخدام خوارزمية Minimax
- 📊 تتبع النتائج وحفظ الإحصائيات
- 👥 تسجيل أسماء اللاعبين
- 🎵 إضافة أصوات وتأثيرات صوتية
- 🏆 نظام نقاط ومستويات
- 🌐 دعم اللعب متعدد اللاعبين عبر الإنترنت

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **خطأ في تثبيت Flask**:
   ```bash
   pip install --upgrade pip
   pip install flask
   ```

2. **المنفذ مستخدم**:
   - غيّر رقم المنفذ في `app.py` من 5544 إلى رقم آخر

3. **مشاكل في التصميم**:
   - تأكد من وجود مجلد `static` و `templates`
   - تحقق من مسارات الملفات

## 📝 الترخيص

هذا المشروع مفتوح المصدر ويمكن استخدامه وتطويره بحرية.

## 👨‍💻 المطور

تم تطوير هذا المشروع كمثال تعليمي لتطوير ألعاب الويب باستخدام Python و Flask.

---

**استمتع باللعب! 🎉**
