from flask import Flask, render_template, request, jsonify
import random

app = Flask(__name__)

class XOGame:
    def __init__(self):
        self.board = [['', '', ''], ['', '', ''], ['', '', '']]
        self.current_player = 'X'
        self.game_over = False
        self.winner = None
        self.is_draw = False
        
    def make_move(self, row, col):
        """تسجيل حركة اللاعب"""
        if self.board[row][col] == '' and not self.game_over:
            self.board[row][col] = self.current_player
            
            # التحقق من الفوز
            if self.check_winner():
                self.winner = self.current_player
                self.game_over = True
            elif self.is_board_full():
                self.is_draw = True
                self.game_over = True
            else:
                # تبديل اللاعب
                self.current_player = 'O' if self.current_player == 'X' else 'X'
            
            return True
        return False
    
    def check_winner(self):
        """التحقق من وجود فائز"""
        # فحص الصفوف
        for row in self.board:
            if row[0] == row[1] == row[2] != '':
                return True
        
        # فحص الأعمدة
        for col in range(3):
            if self.board[0][col] == self.board[1][col] == self.board[2][col] != '':
                return True
        
        # فحص الأقطار
        if self.board[0][0] == self.board[1][1] == self.board[2][2] != '':
            return True
        if self.board[0][2] == self.board[1][1] == self.board[2][0] != '':
            return True
        
        return False
    
    def is_board_full(self):
        """التحقق من امتلاء اللوحة"""
        for row in self.board:
            for cell in row:
                if cell == '':
                    return False
        return True
    
    def reset_game(self):
        """إعادة تشغيل اللعبة"""
        self.board = [['', '', ''], ['', '', ''], ['', '', '']]
        self.current_player = 'X'
        self.game_over = False
        self.winner = None
        self.is_draw = False
    
    def get_game_state(self):
        """الحصول على حالة اللعبة الحالية"""
        return {
            'board': self.board,
            'current_player': self.current_player,
            'game_over': self.game_over,
            'winner': self.winner,
            'is_draw': self.is_draw
        }

# إنشاء مثيل من اللعبة
game = XOGame()

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html')

@app.route('/api/game_state')
def get_game_state():
    """الحصول على حالة اللعبة"""
    return jsonify(game.get_game_state())

@app.route('/api/make_move', methods=['POST'])
def make_move():
    """تسجيل حركة اللاعب"""
    data = request.get_json()
    row = data.get('row')
    col = data.get('col')
    
    if row is not None and col is not None:
        success = game.make_move(row, col)
        return jsonify({
            'success': success,
            'game_state': game.get_game_state()
        })
    
    return jsonify({'success': False, 'error': 'Invalid move data'})

@app.route('/api/reset_game', methods=['POST'])
def reset_game():
    """إعادة تشغيل اللعبة"""
    game.reset_game()
    return jsonify({
        'success': True,
        'game_state': game.get_game_state()
    })

if __name__ == '__main__':
    print("🎮 لعبة XO جاهزة!")
    print("🌐 افتح المتصفح على: http://localhost:5544")
    app.run(debug=True, host='0.0.0.0', port=5544)
