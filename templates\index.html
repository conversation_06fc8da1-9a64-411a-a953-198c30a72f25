<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لعبة XO - تيك تاك تو</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <header>
            <h1>🎮 لعبة XO - تيك تاك تو</h1>
            <div class="game-info">
                <div class="current-player">
                    <span>دور اللاعب: </span>
                    <span id="current-player-display" class="player-x">X</span>
                </div>
                <button id="reset-btn" class="reset-button">🔄 لعبة جديدة</button>
            </div>
        </header>

        <main>
            <div id="game-message" class="game-message hidden"></div>
            
            <div class="game-board" id="game-board">
                <div class="cell" data-row="0" data-col="0"></div>
                <div class="cell" data-row="0" data-col="1"></div>
                <div class="cell" data-row="0" data-col="2"></div>
                <div class="cell" data-row="1" data-col="0"></div>
                <div class="cell" data-row="1" data-col="1"></div>
                <div class="cell" data-row="1" data-col="2"></div>
                <div class="cell" data-row="2" data-col="0"></div>
                <div class="cell" data-row="2" data-col="1"></div>
                <div class="cell" data-row="2" data-col="2"></div>
            </div>
        </main>

        <footer>
            <p>تم تطوير اللعبة باستخدام Python و Flask</p>
        </footer>
    </div>

    <script>
        class XOGameClient {
            constructor() {
                this.gameBoard = document.getElementById('game-board');
                this.currentPlayerDisplay = document.getElementById('current-player-display');
                this.gameMessage = document.getElementById('game-message');
                this.resetBtn = document.getElementById('reset-btn');
                
                this.initializeEventListeners();
                this.updateGameState();
            }
            
            initializeEventListeners() {
                // إضافة مستمعي الأحداث للخلايا
                this.gameBoard.addEventListener('click', (e) => {
                    if (e.target.classList.contains('cell')) {
                        const row = parseInt(e.target.dataset.row);
                        const col = parseInt(e.target.dataset.col);
                        this.makeMove(row, col);
                    }
                });
                
                // إضافة مستمع حدث لزر إعادة التشغيل
                this.resetBtn.addEventListener('click', () => {
                    this.resetGame();
                });
            }
            
            async makeMove(row, col) {
                try {
                    const response = await fetch('/api/make_move', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ row, col })
                    });
                    
                    const data = await response.json();
                    if (data.success) {
                        this.updateUI(data.game_state);
                    }
                } catch (error) {
                    console.error('خطأ في تسجيل الحركة:', error);
                }
            }
            
            async resetGame() {
                try {
                    const response = await fetch('/api/reset_game', {
                        method: 'POST'
                    });
                    
                    const data = await response.json();
                    if (data.success) {
                        this.updateUI(data.game_state);
                    }
                } catch (error) {
                    console.error('خطأ في إعادة تشغيل اللعبة:', error);
                }
            }
            
            async updateGameState() {
                try {
                    const response = await fetch('/api/game_state');
                    const gameState = await response.json();
                    this.updateUI(gameState);
                } catch (error) {
                    console.error('خطأ في تحديث حالة اللعبة:', error);
                }
            }
            
            updateUI(gameState) {
                // تحديث اللوحة
                const cells = this.gameBoard.querySelectorAll('.cell');
                cells.forEach((cell, index) => {
                    const row = Math.floor(index / 3);
                    const col = index % 3;
                    const value = gameState.board[row][col];
                    
                    cell.textContent = value;
                    cell.className = 'cell';
                    
                    if (value === 'X') {
                        cell.classList.add('player-x');
                    } else if (value === 'O') {
                        cell.classList.add('player-o');
                    }
                    
                    if (value !== '') {
                        cell.classList.add('filled');
                    }
                });
                
                // تحديث اللاعب الحالي
                this.currentPlayerDisplay.textContent = gameState.current_player;
                this.currentPlayerDisplay.className = gameState.current_player === 'X' ? 'player-x' : 'player-o';
                
                // تحديث رسالة اللعبة
                if (gameState.game_over) {
                    if (gameState.winner) {
                        this.showMessage(`🎉 اللاعب ${gameState.winner} فاز!`, 'winner');
                    } else if (gameState.is_draw) {
                        this.showMessage('🤝 تعادل!', 'draw');
                    }
                } else {
                    this.hideMessage();
                }
            }
            
            showMessage(text, type) {
                this.gameMessage.textContent = text;
                this.gameMessage.className = `game-message ${type}`;
                this.gameMessage.classList.remove('hidden');
            }
            
            hideMessage() {
                this.gameMessage.classList.add('hidden');
            }
        }
        
        // تشغيل اللعبة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            new XOGameClient();
        });
    </script>
</body>
</html>
